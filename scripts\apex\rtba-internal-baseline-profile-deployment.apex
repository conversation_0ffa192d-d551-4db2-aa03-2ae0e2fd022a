/**
 * Manual Steps:
 * 1. Rename the 'RTBA Internal User - Baseline' to 'RTBA Internal User - Baseline - orig' in the destination org
 * 2. Deploy the 'RTBA Internal User - Baseline' profile metadata with the following command:
 *    > sf project deploy start -m 'Profile:RTBA Internal User - Baseline' -m 'CustomApplication:*' -m 'Layout:*' -m 'RecordType:*' -m 'CustomTab:*'
 */

/**
 * The 'RTBA Internal User - Baseline' profile metadata should contain the following sections:
 *
 * 1. Application Visibilities (47 applications)
 * 	Visible Applications:
 * 	RTBA_Register (default: true, visible: true)
 *		RTBA_Register_Console (default: false, visible: true)
 *
 * 	Hidden Applications: (45 applications)
 * 	including IDSC, IDSC_Console, LoggerConsole, Reconciliation, Recoupment, Sales, all standard Salesforce applications)
 *
 *	2. Layout Assignments (24 layouts)
 *		Account-Organisation Layout (Business_Account record type)
 *		Bond_Party__c-Bond Party Layout
 *		Bond_Party_History__c-Bond Party History Layout
 *		Bond__c-Bond Layout (default and RTBA_Bond record type)
 *		ContentVersion layouts (multiple record types)
 *		Transaction__c layouts (multiple record types: Bond_Lodgement, Claim, Modification_Bond_Lodgement, Transfer)
 *		Other custom object layouts (9 in total)
 *		Various other custom object layouts
 *
 * 3. Record Type Visibilities (2 record types)
 *		ContentVersion.CommunityUser (default: false, visible: false)
 *		ContentVersion.RTBA (default: true, visible: true, personAccountDefault: true)
 *
 * 4. Tab Visibilities (149+ tabs - ALL HIDDEN)
 *		All tabs are set to "Hidden" including:
 *		Custom object tabs (Bond__c, Transaction__c, etc.)
 *		Standard Salesforce tabs (Account, Case, Opportunity, etc.)
 *		Logger and administrative tabs
 *
 * 5. User License
 *		Salesforce (full Salesforce license)
 *
 * 6. User Permissions (1 permission)
 *		ViewSetup: disabled
 *
 * 7. Profile Type
 *		custom: true (this is a custom profile)
 */

String sourceProfileName = 'RTBA Internal User - Baseline - orig';
String targetProfileName = 'RTBA Internal User - Baseline';

try {
	// Step 1: Validate and get source profile
	List<Profile> sourceProfiles = [
		SELECT Id, Name, UserLicenseId
		FROM Profile
		WHERE Name = :sourceProfileName
		LIMIT 1
	];

	if (sourceProfiles.isEmpty()) {
		throw new QueryException(
			'ERROR: Source profile "' + sourceProfileName + '" not found'
		);
	}

	Profile sourceProfile = sourceProfiles[0];

	// Step 2: Validate and get target profile
	List<Profile> targetProfiles = [
		SELECT Id, Name, UserLicenseId
		FROM Profile
		WHERE Name = :targetProfileName
		LIMIT 1
	];

	if (targetProfiles.isEmpty()) {
		throw new QueryException(
			'ERROR: Target profile "' + targetProfileName + '" not found'
		);
	}

	Profile targetProfile = targetProfiles[0];

	// Step 3: Get all users assigned to source profile
	List<User> usersToMove = [
		SELECT Id, Username, Name, Email, ProfileId, IsActive
		FROM User
		WHERE ProfileId = :sourceProfile.Id
	];

	if (usersToMove.isEmpty()) {
		throw new QueryException(
			'INFO: No users found assigned to profile "' +
				sourceProfileName +
				'"'
		);
	}

	// Step 4: Update users to new profile
	List<User> usersToUpdate = new List<User>();
	Integer activeUsersCount = 0;
	Integer inactiveUsersCount = 0;

	for (User u : usersToMove) {
		u.ProfileId = targetProfile.Id;
		usersToUpdate.add(u);

		if (u.IsActive) {
			activeUsersCount++;
		} else {
			inactiveUsersCount++;
		}
	}

	// Step 5: Perform the update
	List<Database.SaveResult> results = Database.update(usersToUpdate, false);
} catch (Exception e) {
	System.debug(
		LoggingLevel.ERROR,
		'EXCEPTION: ' +
			e.getMessage() +
			'\nStack Trace: ' +
			e.getStackTraceString()
	);
}